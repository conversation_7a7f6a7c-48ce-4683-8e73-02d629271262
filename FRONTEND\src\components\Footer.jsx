import { Link } from "react-router-dom";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-600 rounded flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
              </svg>
            </div>
            <span className="text-sm font-medium text-gray-900">DevConnect</span>
          </div>

          {/* Links and Copyright */}
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 text-xs text-gray-500">
            <div className="flex items-center space-x-4">
              <Link to="/privacy" className="hover:text-gray-700">Privacy Policy</Link>
              <Link to="/terms" className="hover:text-gray-700">Terms of Service</Link>
              <Link to="/contact" className="hover:text-gray-700">Contact</Link>
            </div>
            <span>Copyright © {currentYear} DevConnect Inc.</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
