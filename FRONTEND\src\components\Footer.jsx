import { Link } from "react-router-dom";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12">
          {/* Footer Links Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8">
            {/* Platform */}
            <div>
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-4">Platform</h3>
              <ul className="space-y-3">
                <li><Link to="/feed" className="text-sm text-gray-600 hover:text-gray-900">Feed</Link></li>
                <li><Link to="/projects" className="text-sm text-gray-600 hover:text-gray-900">Projects</Link></li>
                <li><Link to="/developers" className="text-sm text-gray-600 hover:text-gray-900">Developers</Link></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-4">Company</h3>
              <ul className="space-y-3">
                <li><Link to="/about" className="text-sm text-gray-600 hover:text-gray-900">About</Link></li>
                <li><Link to="/careers" className="text-sm text-gray-600 hover:text-gray-900">Careers</Link></li>
                <li><Link to="/contact" className="text-sm text-gray-600 hover:text-gray-900">Contact</Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-4">Support</h3>
              <ul className="space-y-3">
                <li><Link to="/help" className="text-sm text-gray-600 hover:text-gray-900">Help Center</Link></li>
                <li><Link to="/docs" className="text-sm text-gray-600 hover:text-gray-900">Documentation</Link></li>
                <li><Link to="/community" className="text-sm text-gray-600 hover:text-gray-900">Community</Link></li>
              </ul>
            </div>

            {/* Legal */}
            <div>
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-4">Legal</h3>
              <ul className="space-y-3">
                <li><Link to="/privacy" className="text-sm text-gray-600 hover:text-gray-900">Privacy Policy</Link></li>
                <li><Link to="/terms" className="text-sm text-gray-600 hover:text-gray-900">Terms of Service</Link></li>
                <li><Link to="/cookies" className="text-sm text-gray-600 hover:text-gray-900">Cookie Policy</Link></li>
              </ul>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-200 pt-8">
            {/* Bottom Section */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
              {/* Logo and Copyright */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-gray-900">DevConnect</span>
                </div>
                <span className="text-xs text-gray-500">Copyright © {currentYear} DevConnect Inc. All rights reserved.</span>
              </div>

              {/* Country/Region */}
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <span>United States</span>
                <span>|</span>
                <Link to="/sitemap" className="hover:text-gray-700">Site Map</Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
